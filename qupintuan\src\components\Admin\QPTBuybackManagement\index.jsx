// src/components/Admin/QPTBuybackManagement/index.jsx
import { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { toast } from 'react-hot-toast'
import { formatUnits } from 'ethers'
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts'
import { createPublicClient, createWalletClient, custom, http } from 'viem'
import { bscTestnet } from 'viem/chains'
import { createQPTBuybackRoom, checkIsAdmin } from '@/apis/qptBuybackApi'
import { useQPTBuybackRooms } from '@/hooks/useQPTBuybackRooms'
import UnifiedRoomCard from '@/components/QPTBuyback/UnifiedRoomCard'
import Pagination from '@/components/Common/Pagination'
import { queryContractBalances, querySystemOverview } from '@/utils/contractBalanceQueryFixed'
import './index.css'
// 导入前端QPT回购页面的样式，确保布局一致性
import '@/components/Finance/QPTBuyback/index.css'

export default function QPTBuybackManagement() {
  const { address: account } = useAccount()

  // 获取房间数据
  const {
    rooms,
    isLoading: isLoadingRooms,
    isError,
    error,
    refreshRooms
  } = useQPTBuybackRooms(false, true) // false表示不是"我的房间"页面，true表示是管理后台页面

  // 状态管理
  const [buybackData, setBuybackData] = useState({
    availableBuybackPool: '0', // 改名为与前端一致
    contractUSDTBalance: '0',
    contractQPTBalance: '0',
    contractBNBBalance: '0',
    roomCounter: 0,
    dailyStats: null
  })

  const [isLoading, setIsLoading] = useState(false)
  const [isCreatingRoom, setIsCreatingRoom] = useState(false)
  const [creatingTier, setCreatingTier] = useState(null)
  const [isAdmin, setIsAdmin] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [roomsPerPage] = useState(6)

  // 创建客户端
  const publicClient = createPublicClient({
    chain: bscTestnet,
    transport: http('https://bsc-testnet.public.blastapi.io')
  })

  // 加载回购数据
  const loadBuybackData = async () => {
    if (!account) return

    setIsLoading(true)
    try {
      // 使用优化的合约余额查询服务
      const [balances, overview] = await Promise.all([
        queryContractBalances(),
        querySystemOverview()
      ])

      // 从查询结果中提取QPT回购相关数据
      const qptBuybackData = balances?.qptBuyback || {}
      const buybackStats = overview?.buybackStats || {}

      // 备用方案：如果查询服务失败，使用直接合约查询（与前端保持一致）
      if (!qptBuybackData.availableBuybackPool && !overview?.qptBuyback?.availableBuybackPool) {
        const buybackAddress = CONTRACT_ADDRESSES[97].QPTBuyback
        const usdtAddress = CONTRACT_ADDRESSES[97].USDT

        if (!buybackAddress || !usdtAddress) {
          throw new Error('合约地址未配置')
        }

        // 使用与前端相同的查询方式
        const [balanceDetails, roomCounter, contractQPTBalance, contractBNBBalance] = await Promise.all([
          // 使用getBalanceDetails获取可用回购池（与前端一致）
          publicClient.readContract({
            address: buybackAddress,
            abi: ABIS.QPTBuyback,
            functionName: 'getBalanceDetails'
          }),
          publicClient.readContract({
            address: buybackAddress,
            abi: ABIS.QPTBuyback,
            functionName: 'roomCounter'
          }),
          publicClient.readContract({
            address: CONTRACT_ADDRESSES[97].QPTToken,
            abi: ABIS.QPTToken,
            functionName: 'balanceOf',
            args: [buybackAddress]
          }),
          publicClient.getBalance({
            address: buybackAddress
          })
        ])

        // 从getBalanceDetails返回值中提取数据
        const contractUSDTBalance = balanceDetails[0]; // 合约总USDT余额
        const availableBuybackPool = balanceDetails[1]; // 可用回购池余额

        setBuybackData({
          availableBuybackPool: availableBuybackPool ? formatUnits(availableBuybackPool, 6) : '0',
          contractUSDTBalance: contractUSDTBalance ? formatUnits(contractUSDTBalance, 6) : '0',
          contractQPTBalance: contractQPTBalance ? formatUnits(contractQPTBalance, 18) : '0',
          contractBNBBalance: contractBNBBalance ? formatUnits(contractBNBBalance, 18) : '0',
          roomCounter: Number(roomCounter),
          dailyStats: {
            dailyUSDTIncome: '0.00',
            dailyUSDTOutgoing: '0.00',
            dailyNewRooms: 0,
            dailyCompletedRooms: 0,
            note: '直接合约查询（备用方案）'
          }
        })
      } else {
        // 使用查询服务的结果
        const qptBuybackFromOverview = overview?.qptBuyback || {}

        setBuybackData({
          availableBuybackPool: qptBuybackFromOverview.availableBuybackPool || qptBuybackData.availableBuybackPool || qptBuybackData.totalBuybackPool || '0',
          contractUSDTBalance: qptBuybackFromOverview.contractUSDTBalance || qptBuybackData.usdtBalance || '0',
          contractQPTBalance: qptBuybackFromOverview.qptBalance || qptBuybackData.qptBalance || '0',
          contractBNBBalance: qptBuybackFromOverview.bnbBalance || qptBuybackData.bnbBalance || '0',
          roomCounter: buybackStats.totalRooms || 0,
          dailyStats: {
            dailyUSDTIncome: buybackStats.dailyUSDTIncome || '0.00',
            dailyUSDTOutgoing: buybackStats.dailyUSDTOutgoing || '0.00',
            dailyNewRooms: buybackStats.dailyNewRooms || 0,
            dailyCompletedRooms: buybackStats.dailyCompletedRooms || 0,
            note: '使用优化的合约查询服务'
          }
        })
      }
    } catch (error) {
      console.error('加载回购数据失败:', error)
      toast.error('加载回购数据失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 分页逻辑
  const totalRooms = rooms?.length || 0
  const totalPages = Math.ceil(totalRooms / roomsPerPage)
  const startIndex = (currentPage - 1) * roomsPerPage
  const endIndex = startIndex + roomsPerPage
  const currentRooms = rooms?.slice(startIndex, endIndex) || []

  // 筛选满员房间（需要开奖的房间）
  const fullRooms = rooms?.filter(room =>
    room.participantsCount >= 8 &&
    !room.winner &&
    room.timeLeft > 0
  ) || []

  // 检查管理员权限
  const checkAdminPermission = async () => {
    if (!account) {
      setIsAdmin(false)
      return
    }

    try {
      const adminStatus = await checkIsAdmin(account)
      setIsAdmin(adminStatus)
    } catch (error) {
      console.error('检查管理员权限失败:', error)
      setIsAdmin(false)
    }
  }

  // 创建QPT回购房间
  const handleCreateRoom = async (tier) => {
    if (!account) {
      toast.error('请先连接钱包')
      return
    }

    if (!isAdmin) {
      toast.error('只有管理员可以创建QPT回购房间')
      return
    }

    setIsCreatingRoom(true)
    setCreatingTier(tier)

    try {
      const result = await createQPTBuybackRoom({
        tier,
        account
      })



      // 刷新数据
      setTimeout(() => {
        loadBuybackData()
      }, 2000)

    } catch (error) {
      // toast已在API中处理
    } finally {
      setIsCreatingRoom(false)
      setCreatingTier(null)
    }
  }

  // 处理房间操作的回调函数
  const handleJoinRoom = async (roomId) => {
    // 管理员界面不需要参与房间功能
    toast.error('管理员界面不支持参与房间，请前往用户界面')
  }

  const handleClaimReward = async (roomId) => {
    // 管理员界面不需要领取奖励功能
    toast.error('管理员界面不支持领取奖励，请前往用户界面')
  }

  const handleExpireRoom = async (roomId) => {
    // 管理员界面不需要过期房间功能
    toast.error('管理员界面不支持过期房间操作，请前往用户界面')
  }

  const showSuccessMessage = (message) => {
    toast.success(message)
  }

  const showErrorMessage = (message) => {
    toast.error(message)
  }

  const formatErrorMessage = (error) => {
    return error?.message || error?.toString() || '未知错误'
  }

  useEffect(() => {
    loadBuybackData()
    checkAdminPermission()
  }, [account])

  return (
    <div className="qpt-buyback-management qpt-buyback">
      <div className="management-header">
        <div className="header-content">
          <h2>🔄 QPT回购管理</h2>
          <p>管理QPT回购房间和资金池</p>
        </div>
        <div className="header-actions">
          <button
            className="refresh-btn"
            onClick={() => {
              loadBuybackData()
              refreshRooms()
            }}
            disabled={isLoading || isLoadingRooms}
          >
            {(isLoading || isLoadingRooms) ? '🔄 加载中...' : '🔄 刷新数据'}
          </button>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="buyback-stats">
        <div className="stat-item">
          <span className="stat-label">可用回购池</span>
          <span className="stat-value">{parseFloat(buybackData.availableBuybackPool).toFixed(2)} USDT</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">合约USDT余额</span>
          <span className="stat-value">{parseFloat(buybackData.contractUSDTBalance).toFixed(2)} USDT</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">合约QPT余额</span>
          <span className="stat-value">{parseFloat(buybackData.contractQPTBalance).toFixed(2)} QPT</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">合约BNB余额</span>
          <span className={`stat-value ${parseFloat(buybackData.contractBNBBalance) < 0.01 ? 'low-balance' : ''}`}>
            {parseFloat(buybackData.contractBNBBalance).toFixed(3)} BNB
          </span>
        </div>
        <div className="stat-item">
          <span className="stat-label">房间总数</span>
          <span className="stat-value">{buybackData.roomCounter}</span>
        </div>
      </div>

      {/* QPT回购房间创建 */}
      {isAdmin && (
        <div className="qpt-tier-section">
          <h3 className="tier-section-title">🎯 创建QPT回购房间</h3>
          <div className="qpt-tier-buttons">
            {[100, 200, 500, 1000].map((tier, index) => (
              <button
                key={tier}
                className={`qpt-tier-btn qpt-tier-${index}`}
                onClick={() => handleCreateRoom(tier)}
                disabled={isCreatingRoom}
                style={isCreatingRoom && creatingTier === tier ? {
                  background: 'rgba(156, 163, 175, 0.5) !important',
                  cursor: 'not-allowed'
                } : {}}
              >
                {isCreatingRoom && creatingTier === tier ? (
                  <>
                    <span>创建中...</span>
                    <small>请等待交易确认</small>
                  </>
                ) : (
                  <>
                    <span>{tier} QPT档位</span>
                    <small>一键创建回购房间</small>
                  </>
                )}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 满员房间提醒 */}
      {isAdmin && fullRooms.length > 0 && (
        <div className="full-rooms-alert">
          <div className="alert-header">
            <h3>⚠️ 需要开奖的房间</h3>
            <span className="alert-count">{fullRooms.length} 个房间</span>
          </div>
          <p>以下房间已满员，需要管理员进行开奖操作：</p>
          <div className="full-rooms-list">
            {fullRooms.map(room => (
              <div key={room.id} className="full-room-item">
                <span>房间 #{room.id}</span>
                <span>{room.tier || 0} QPT档位</span>
                <span className="participants-count">{room.participantsCount}/8 人</span>
                <span className="status-urgent">等待开奖</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 房间列表 */}
      <div className="rooms-section">
        <div className="rooms-header">
          <h3>🏠 QPT回购房间列表</h3>
          <div className="rooms-stats">
            <span>总房间数: {totalRooms}</span>
            {isAdmin && <span>待开奖: {fullRooms.length}</span>}
          </div>
        </div>

        {isLoadingRooms ? (
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>加载房间数据中...</p>
          </div>
        ) : isError ? (
          <div className="error-state">
            <div className="error-icon">❌</div>
            <h3>加载失败</h3>
            <p>{error?.message || '无法加载房间数据'}</p>
            <button onClick={refreshRooms} className="retry-btn">
              重试
            </button>
          </div>
        ) : currentRooms.length > 0 ? (
          <>
            <div className="rooms-list">
              {currentRooms.map(room => (
                <UnifiedRoomCard
                  key={room.id}
                  room={room}
                  account={account}
                  onJoinRoom={handleJoinRoom}
                  onClaimReward={handleClaimReward}
                  onExpireRoom={handleExpireRoom}
                  onRefreshRooms={refreshRooms}
                  isClaimingReward={false}
                  isProcessingExpire={false}
                  showSuccessMessage={showSuccessMessage}
                  showErrorMessage={showErrorMessage}
                  formatErrorMessage={formatErrorMessage}
                  isAdminView={true}
                />
              ))}
            </div>

            {/* 分页控件 */}
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
              showPageInfo={true}
              showQuickJump={totalPages > 10}
            />
          </>
        ) : (
          <div className="empty-state">
            <div className="empty-icon">💎</div>
            <h3>暂无QPT回购房间</h3>
            <p>目前没有活跃的QPT回购房间</p>
          </div>
        )}
      </div>
    </div>
  )
}
