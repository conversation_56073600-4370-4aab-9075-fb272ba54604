import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import { addExemptRoom, removeExemptRoom, getExemptRooms } from '@/utils/permissionManager';
import './RoomUnlockManager.css';

/**
 * 简化的房间解除锁定管理器
 * 基于新的逻辑：房间锁定状态由前端状态自动判断，管理后台只需要提供解除锁定功能
 */
export default function RoomUnlockManager() {
  const [roomId, setRoomId] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [exemptRooms, setExemptRooms] = useState(getExemptRooms());

  // 刷新豁免房间列表
  const refreshExemptRooms = () => {
    setExemptRooms(getExemptRooms());
  };

  // 解除房间锁定
  const handleUnlockRoom = async () => {
    if (!roomId.trim()) {
      toast.error('请输入房间ID');
      return;
    }

    const roomIdNum = parseInt(roomId.trim());
    if (isNaN(roomIdNum) || roomIdNum < 0) {
      toast.error('请输入有效的房间ID');
      return;
    }

    setIsProcessing(true);
    try {
      // 添加到豁免列表
      addExemptRoom(roomIdNum, `管理员手动解除锁定 - ${new Date().toLocaleString()}`);
      
      toast.success(`房间 #${roomIdNum} 已解除锁定`);
      setRoomId('');
      refreshExemptRooms();
      
      // 提示用户刷新前端页面
      toast.success('请刷新拼团页面查看效果', { duration: 3000 });
      
    } catch (error) {
      console.error('解除房间锁定失败:', error);
      toast.error('解除锁定失败');
    } finally {
      setIsProcessing(false);
    }
  };

  // 重新锁定房间（移除豁免）
  const handleLockRoom = (roomId) => {
    try {
      removeExemptRoom(roomId);
      toast.success(`房间 #${roomId} 已重新锁定`);
      refreshExemptRooms();
      
      // 提示用户刷新前端页面
      toast.success('请刷新拼团页面查看效果', { duration: 3000 });
      
    } catch (error) {
      console.error('重新锁定房间失败:', error);
      toast.error('重新锁定失败');
    }
  };

  // 清理所有已解除锁定的房间
  const handleClearAllExemptRooms = () => {
    const roomCount = Object.keys(exemptRooms).length;
    if (roomCount === 0) {
      toast.error('没有需要清理的房间');
      return;
    }

    if (!confirm(`确定要清理所有 ${roomCount} 个已解除锁定的房间吗？\n\n清理后这些房间将重新受到违规检测。`)) {
      return;
    }

    try {
      // 清空localStorage中的豁免房间列表
      localStorage.removeItem('exempt_rooms');
      toast.success(`已清理 ${roomCount} 个房间的解除锁定状态`);
      refreshExemptRooms();
      
      // 提示用户刷新前端页面
      toast.success('请刷新拼团页面查看效果', { duration: 3000 });
      
    } catch (error) {
      console.error('清理豁免房间失败:', error);
      toast.error('清理失败');
    }
  };

  return (
    <div className="room-unlock-manager">
      <div className="unlock-section">
        <h3>🔓 解除房间锁定</h3>
        <div className="unlock-form">
          <div className="form-group">
            <label>房间ID：</label>
            <input
              type="number"
              value={roomId}
              onChange={(e) => setRoomId(e.target.value)}
              placeholder="输入要解除锁定的房间ID"
              min="0"
              disabled={isProcessing}
            />
          </div>
          <button
            onClick={handleUnlockRoom}
            disabled={isProcessing || !roomId.trim()}
            className="unlock-btn"
          >
            {isProcessing ? '处理中...' : '解除锁定'}
          </button>
        </div>
      </div>

      {Object.keys(exemptRooms).length > 0 && (
        <div className="exempt-rooms-section">
          <div className="exempt-rooms-header">
            <h3>🔒 已解除锁定的房间</h3>
            <button
              onClick={handleClearAllExemptRooms}
              className="clear-all-btn"
              title="清理所有已解除锁定的房间"
            >
              🗑️ 清理全部 ({Object.keys(exemptRooms).length})
            </button>
          </div>
          <div className="exempt-rooms-list">
            {Object.entries(exemptRooms).map(([roomId, info]) => (
              <div key={roomId} className="exempt-room-item">
                <div className="room-info">
                  <span className="room-id">房间 #{roomId}</span>
                  <span className="exempt-time">
                    解除时间: {(() => {
                      // 尝试多种时间戳格式
                      const timestamp = info.timestamp || info.exemptedAt;
                      if (!timestamp) return '未知时间';
                      
                      const date = new Date(timestamp);
                      if (isNaN(date.getTime())) return '时间格式错误';
                      
                      return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                      });
                    })()}
                  </span>
                  {info.reason && (
                    <span className="exempt-reason">原因: {info.reason}</span>
                  )}
                </div>
                <button
                  onClick={() => handleLockRoom(roomId)}
                  className="relock-btn"
                  title="重新锁定此房间"
                >
                  重新锁定
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
