// src/components/Admin/UserManagement/index.jsx
import { useState, useEffect } from 'react';
import { useAccount } from 'wagmi';
import { toast } from 'react-hot-toast';
import { addToBlacklist, removeFromBlacklist, getTotalUsers } from '@/apis/adminApi';
import UserDataQuery from '@/components/Common/UserDataQuery';
import UserLevelManager from '@/components/Admin/UserLevelManager';
import LevelValidationTool from '@/components/Admin/LevelValidationTool';
import LevelFixTool from '@/components/Admin/LevelFixTool';
import RoomUnlockManager from './RoomUnlockManager';
import {
  removeRoomRestrictions,
  isAdmin,
  getExemptRooms
} from '@/utils/permissionManager';
import './index.css';

export default function UserManagement() {
  const { address: account } = useAccount();

  // 状态管理
  const [userData, setUserData] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [userStats, setUserStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    newUsersToday: 0,
    blacklistedUsers: 0,
    lockedRooms: 0, // 被锁定的房间数量
    exemptRooms: 0   // 已解除锁定的房间数量
  });


  const [isLoadingStats, setIsLoadingStats] = useState(false);



  // 加载用户统计数据
  const loadUserStats = async (forceAccurate = false) => {
    setIsLoadingStats(true);
    try {
      // 从合约获取真实的用户统计数据
      const [totalUsers, overview] = await Promise.all([
        getTotalUsers(forceAccurate), // 传递强制同步参数
        (async () => {
          try {
            const { querySystemOverview } = await import('@/utils/contractBalanceQueryFixed');
            return await querySystemOverview();
          } catch (error) {
            console.error('查询系统概览失败:', error);
            return null;
          }
        })()
      ]);

      // 获取用户相关统计
      const userRegistrationStats = overview?.userRegistrationStats || {};

      // 获取违规房间统计
      const exemptRooms = getExemptRooms();

      // 简化逻辑：不再进行复杂的违规检测
      // 锁定房间现在基于前端状态自动判断，管理后台只需要提供解除锁定功能
      let lockedRoomsCount = 0;
      let lockedRoomsList = [];

      // 注意：实际的锁定房间统计现在由前端状态决定
      // 这里只是为了保持界面统计的完整性，实际数字可能不准确
      // 真实的锁定状态由前端 shouldLockRoom 函数基于房间状态判断



      setUserStats({
        totalUsers: totalUsers || userRegistrationStats.totalUsers || 0,
        activeUsers: userRegistrationStats.activeUsers || Math.floor(totalUsers * 0.6),
        newUsersToday: userRegistrationStats.newUsersToday || 0,
        lockedRooms: lockedRoomsCount, // 被锁定的房间数量
        exemptRooms: Object.keys(exemptRooms).length // 已解除锁定的房间数量
      });


    } catch (error) {
      console.error('加载用户统计失败:', error);
      toast.error('加载用户统计失败');
      // 如果合约查询失败，使用基础数据
      setUserStats({
        totalUsers: 0,
        activeUsers: 0,
        newUsersToday: 0,
        lockedRooms: 0,
        exemptRooms: 0
      });
    } finally {
      setIsLoadingStats(false);
    }
  };

  // 处理用户数据变化
  const handleUserDataChange = (newUserData) => {
    setUserData(newUserData);
    console.log('👤 [UserManagement] 用户数据更新:', newUserData);
  };

  // 刷新用户数据
  const refreshUserData = () => {
    if (userData?.address) {
      // 触发UserDataQuery重新查询
      window.dispatchEvent(new CustomEvent('refreshUserData', {
        detail: { address: userData.address }
      }));
    }
  };

  // 组件挂载时加载统计数据
  useEffect(() => {
    loadUserStats();

    // 加载合约查询工具
    import('@/utils/contractLotteryChecker').catch(error => {
      // 静默处理加载失败
    });

    // 加载用户统计测试工具（开发环境）
    if (process.env.NODE_ENV === 'development') {
      // 加载快速用户统计修复工具
      import('@/utils/quickUserCountFix').catch(() => {
        // 静默处理加载失败
      });
    }
  }, []);

  // 添加到黑名单
  const handleAddToBlacklist = async () => {
    if (!userData) return;

    setIsProcessing(true);
    try {
      console.log('🚫 [UserManagement] 添加用户到黑名单:', userData.address);

      // 创建 signer
      const { BrowserProvider } = await import('ethers');
      const provider = new BrowserProvider(window.ethereum);
      const ethersSigner = await provider.getSigner();

      const signer = {
        account: { address: account },
        ...ethersSigner
      };

      // 调用添加黑名单API
      const result = await addToBlacklist({
        userAddress: userData.address,
        signer
      });

      console.log('✅ [UserManagement] 添加黑名单成功:', result);

      // 成功提示
      toast.success('🚫 用户已添加到黑名单', {
        duration: 4000,
        position: 'top-center',
      });

      // 更新用户信息
      setUserData(prev => ({
        ...prev,
        basicInfo: { ...prev.basicInfo, isBlacklisted: true }
      }));

    } catch (error) {
      console.error('❌ [UserManagement] 添加黑名单失败:', error);
      toast.error(`添加黑名单失败: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // 从黑名单移除
  const handleRemoveFromBlacklist = async () => {
    if (!userData) return;

    setIsProcessing(true);
    try {
      console.log('✅ [UserManagement] 从黑名单移除用户:', userData.address);

      // 创建 signer
      const { BrowserProvider } = await import('ethers');
      const provider = new BrowserProvider(window.ethereum);
      const ethersSigner = await provider.getSigner();

      const signer = {
        account: { address: account },
        ...ethersSigner
      };

      // 调用移除黑名单API
      const result = await removeFromBlacklist({
        userAddress: userData.address,
        signer
      });

      console.log('✅ [UserManagement] 移除黑名单成功:', result);

      // 成功提示
      toast.success('✅ 用户已从黑名单移除', {
        duration: 4000,
        position: 'top-center',
      });

      // 更新用户信息
      setUserData(prev => ({
        ...prev,
        basicInfo: { ...prev.basicInfo, isBlacklisted: false }
      }));

    } catch (error) {
      console.error('❌ [UserManagement] 移除黑名单失败:', error);
      toast.error(`移除黑名单失败: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };



  // 限制用户权限
  const handleRestrictUser = async (userAddress, permissionTypes, reason, description) => {
    if (!isAdmin(account)) {
      toast.error('只有管理员可以执行此操作');
      return;
    }

    setIsProcessing(true);
    try {
      adminRestrictUser(account, userAddress, permissionTypes, reason, description);
      await loadUserStats(); // 重新加载统计数据
      toast.success('用户权限已成功限制');
      setShowPermissionModal(false);
    } catch (error) {
      console.error('限制用户权限失败:', error);
      toast.error('限制用户权限失败: ' + error.message);
    } finally {
      setIsProcessing(false);
    }
  };





  // 基于房间ID解除限制
  const handleRemoveRoomRestriction = async (roomId, userAddress) => {
    if (!isAdmin(account)) {
      toast.error('只有管理员可以执行此操作');
      return;
    }

    if (!confirm(`确定要解除房间 #${roomId} 的违规限制吗？\n\n解除后该房间将不再被检测为违规房间。`)) {
      return;
    }

    setIsProcessing(true);
    try {
      // 基于房间ID解除限制
      const result = await removeRoomRestrictions(roomId, userAddress);

      // 刷新相关房间状态
      try {
        const { refreshSpecificRoom } = await import('@/utils/roomStatusRefresher');
        await refreshSpecificRoom(roomId);
      } catch (refreshError) {
        console.warn('刷新房间状态时出现错误:', refreshError);
      }

      // 重新加载统计数据
      await loadUserStats();

      toast.success(`房间 #${roomId} 的违规限制已成功解除`);
    } catch (error) {
      console.error('解除房间限制失败:', error);
      toast.error('解除房间限制失败: ' + error.message);
    } finally {
      setIsProcessing(false);
    }
  };



  return (
    <div className="user-management">
      <div className="management-header">
        <div className="header-content">
          <h2>👥 用户管理</h2>
          <p>管理平台用户和黑名单</p>
        </div>
        <div className="header-actions">
          <button
            className="refresh-btn"
            onClick={() => loadUserStats(false)}
            disabled={isLoadingStats}
          >
            {isLoadingStats ? '🔄 刷新中...' : '🔄 快速刷新'}
          </button>
          <button
            className="refresh-btn accurate"
            onClick={() => loadUserStats(true)}
            disabled={isLoadingStats}
            style={{ marginLeft: '10px', backgroundColor: '#28a745' }}
          >
            {isLoadingStats ? '🔍 准确统计中...' : '🔍 准确统计'}
          </button>
        </div>
      </div>

      {/* 用户统计信息 */}
      <div className="user-stats-section">
        <h3>📊 用户统计</h3>

        <div className="stats-grid">
          <div className="stat-card primary">
            <div className="stat-icon">👥</div>
            <div className="stat-content">
              <div className="stat-value">{userStats.totalUsers}</div>
              <div className="stat-label">注册用户</div>
            </div>
          </div>
          <div className="stat-card success">
            <div className="stat-icon">🟢</div>
            <div className="stat-content">
              <div className="stat-value">{userStats.activeUsers}</div>
              <div className="stat-label">活跃用户</div>
            </div>
          </div>
          <div className="stat-card info">
            <div className="stat-icon">📈</div>
            <div className="stat-content">
              <div className="stat-value">{userStats.newUsersToday}</div>
              <div className="stat-label">今日新增</div>
            </div>
          </div>
          <div className="stat-card warning">
            <div className="stat-icon">🔒</div>
            <div className="stat-content">
              <div className="stat-value">{userStats.lockedRooms}</div>
              <div className="stat-label">违规锁定房间</div>
            </div>
          </div>

          <div className="stat-card success">
            <div className="stat-icon">✅</div>
            <div className="stat-content">
              <div className="stat-value">{userStats.exemptRooms || 0}</div>
              <div className="stat-label">已解除锁定房间</div>
            </div>
          </div>
        </div>
      </div>



      {/* 统一用户数据查询 */}
      <div className="user-query-section">
        <UserDataQuery
          onUserDataChange={handleUserDataChange}
          showFullDetails={true}
          placeholder="请输入用户钱包地址进行管理"
          buttonText="🔍 查询用户"
        />
      </div>

      {/* 用户管理操作 */}
      {userData && (
        <div className="user-management-actions">
          <div className="management-card">
            <div className="management-header">
              <h3>🛠️ 用户管理操作</h3>
              <div className="user-summary">
                <span className="user-type-badge">{userData.summary.userType}</span>
                <span className="risk-level-badge">{userData.summary.riskLevel}</span>
              </div>
            </div>

            <div className="management-actions">
              {userData.basicInfo.isBlacklisted ? (
                <button
                  className="action-btn remove-blacklist"
                  onClick={handleRemoveFromBlacklist}
                  disabled={isProcessing}
                >
                  {isProcessing ? '⏳ 处理中...' : '✅ 移除黑名单'}
                </button>
              ) : (
                <button
                  className="action-btn add-blacklist"
                  onClick={handleAddToBlacklist}
                  disabled={isProcessing}
                >
                  {isProcessing ? '⏳ 处理中...' : '🚫 添加黑名单'}
                </button>
              )}

              <button className="action-btn view-transactions">
                📊 查看交易记录
              </button>

              <button
                className="action-btn manage-permissions"
                onClick={() => {
                  setSelectedUser(userData.address);
                  setShowPermissionModal(true);
                }}
              >
                🔒 权限管理
              </button>
            </div>

            <div className="quick-stats">
              <div className="stat-item">
                <span className="stat-label">总价值:</span>
                <span className="stat-value">{userData.summary.totalValue} USDT</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">活跃度:</span>
                <span className="stat-value">{userData.activityScore}/100</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">质押状态:</span>
                <span className="stat-value">{userData.nodeStaking.isStaking ? '质押中' : '未质押'}</span>
              </div>
            </div>
          </div>

          {/* 用户等级管理 */}
          <UserLevelManager
            userData={userData}
            onLevelUpdated={refreshUserData}
          />

          {/* 等级验证工具 */}
          <LevelValidationTool userData={userData} />

          {/* 批量等级修复工具 */}
          <LevelFixTool />
        </div>
      )}







      {/* 房间解除锁定管理 */}
      <RoomUnlockManager />
    </div>
  );
}
